package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.utils.BeanMapUtilByReflect;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebSocketUtils;
import com.yunqu.work.utils.WebhookKey;

public class TaskNoticeService extends AppBaseService{

	private static class Holder{
		private static TaskNoticeService service=new TaskNoticeService();
	}
	public static TaskNoticeService getService(){
		return Holder.service;
	}
	
	public void noticeTask(String taskId,String title,String content){
		//超时未完成提醒
		try {
			JSONObject jsonObject=getQuery().queryForRow("select * from yq_task where task_id = ?",new Object[]{taskId},new JSONMapperImpl());
			String creator=jsonObject.getString("CREATOR");
			String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
			
			MessageModel model=new MessageModel();
			model.setFkId(jsonObject.getString("TASK_ID"));
			int taskState =jsonObject.getIntValue("TASK_STATE");
			if(taskState==30){
				model.setReceiver(creator);
				model.setSender(assignUserId);
				WebSocketUtils.sendMessage(new MsgModel(creator,title,content));
			}else{
				model.setReceiver(assignUserId);
				model.setSender(creator);
				WebSocketUtils.sendMessage(new MsgModel(assignUserId,title,content));
			}
			model.setTitle(title);
			model.setDesc(jsonObject.getString("TASK_DESC"));
			jsonObject.put("NOTICE_NAME",content);
			model.setData(jsonObject);
			model.setTplName("noticeTask.html");
			try {
				EmailService.getService().sendTaskEmail(model);
			} catch (Exception e) {
				this.getLogger().error(null,e);
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
	public void overtimeTask(){
			//超时未完成提醒
			try {
				String current=EasyDate.getCurrentDateString();
				EasyQuery query=getQuery();
				query.setMaxRow(200);
				List<JSONObject> list=query.queryForList("select *,count(1) task_count,group_concat(task_name separator '<br>') task_names from yq_task where task_state in(10,20) and DEADLINE_AT <= ? GROUP BY ASSIGN_USER_ID order by DEADLINE_AT desc",new Object[]{current},new JSONMapperImpl());
				if(list!=null&&list.size()>0){
					for(JSONObject jsonObject:list){
						String creator = jsonObject.getString("CREATOR");
						String assignUserId = jsonObject.getString("ASSIGN_USER_ID");
						String assignUserName = jsonObject.getString("ASSIGN_USER_NAME");
						String assignDeptId=jsonObject.getString("ASSIGN_DEPT_ID");
						String taskName=jsonObject.getString("TASK_NAME");
						
						String taskNames = jsonObject.getString("TASK_NAMES");
						int taskCount = jsonObject.getIntValue("TASK_COUNT");
						
						
						MessageModel model=new MessageModel();
						model.setFkId(jsonObject.getString("TASK_ID"));
						model.setReceiver(assignUserId);
						model.setSender(creator);
						
						if(taskCount>1) {
							model.setTitle("任务超时("+taskCount+")");
							jsonObject.put("TASK_NAME",taskNames);
							model.setDesc(taskNames);
							jsonObject.put("NOTICE_NAME","任务("+taskCount+")目前已超时,请尽快处理");
							jsonObject.put("TASK_DESC","");
						}else {
							model.setTitle("任务超时|"+taskName);
							model.setDesc(jsonObject.getString("TASK_DESC"));
							jsonObject.put("NOTICE_NAME","任务截至时间至"+jsonObject.getString("DEADLINE_AT")+",目前已超时,请尽快处理");
						}
						
						WeChatWebhookSender.sendMarkdownMessage("dept@"+assignDeptId, assignUserName+":"+model.getTitle(),jsonObject.getString("NOTICE_NAME"));
						
						model.setData(jsonObject);
						model.setTplName("noticeTask.html");
						WxMsgService.getService().sendUnFinshTaskMsg(model);
						try {
							WebSocketUtils.sendMessage(new MsgModel(assignUserId,"任务超时提醒",taskName));
							EmailService.getService().sendTaskEmail(model);
						} catch (Exception e) {
							this.getLogger().error(null,e);
						}
					}
				}
			} catch (Exception e) {
				this.getLogger().error(null,e);
			}
		
	}
	
	//待办提醒
	public  void waitTask(){
		String current=EasyDate.getCurrentDateString();
		getLogger().info("执行待办提醒任务....");
		try {
			EasyQuery query=getQuery();
			query.setMaxRow(200);
			List<JSONObject> list = query.queryForList("select *,count(1) task_count,group_concat(task_name separator  '<br>') task_names from yq_task where task_state = 10 and PLAN_STARTED_AT > ?  GROUP BY ASSIGN_USER_ID order by CREATE_TIME desc",new Object[]{current},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("CREATOR");
					String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
					String taskName = jsonObject.getString("TASK_NAME");
					String taskNames = jsonObject.getString("TASK_NAMES");
					String deadlineAt=jsonObject.getString("DEADLINE_AT");
					
					int taskCount = jsonObject.getIntValue("TASK_COUNT");
					MessageModel model=new MessageModel();
					model.setFkId(jsonObject.getString("TASK_ID"));
					model.setReceiver(assignUserId);
					model.setSender(creator);
					
					if(taskCount>1) {
						model.setTitle("待办任务("+taskCount+")");
						model.setDesc(taskNames);
						jsonObject.put("NOTICE_NAME","您有"+taskCount+"个任务未处理,请尽快处理完毕");
						jsonObject.put("TASK_NAME",taskNames );
						jsonObject.put("TASK_DESC","");
					}else {
						model.setTitle("待办任务|"+taskName);
						model.setDesc(jsonObject.getString("TASK_DESC"));
						jsonObject.put("NOTICE_NAME","您有"+taskCount+"个任务未处理,请在"+deadlineAt+"之前处理完毕");
					}
					
					model.setData(jsonObject);
					
					model.setTplName("noticeTask.html");
					WxMsgService.getService().sendNewTaskMsg(model);
					WebSocketUtils.sendMessage(new MsgModel(assignUserId,"请及时处理待办任务",taskName));
					try {
						EmailService.getService().sendTaskEmail(model);
					} catch (Exception e) {
						this.getLogger().error(null,e);
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
	public  void wxMsgWaitTask(){
		if(!KqNoticeService.getService().kqFlag()) {
			return;
		}
		String current = DateUtils.getBeforeDate(31, "yyyy-MM-dd HH:mm:ss");
		getLogger().info("wxMsgWaitTask执行待办提醒任务...."+current);
		try {
			EasyQuery query = getQuery();
			query.setMaxRow(200);
			List<JSONObject> list = query.queryForList("select ASSIGN_DEPT_ID,ASSIGN_USER_NAME,count(1) task_count,group_concat(task_name separator '；') task_names from yq_task where task_state in(10,20) and PLAN_STARTED_AT > ?  GROUP BY ASSIGN_USER_ID order by task_count desc",new Object[]{current},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				Map<String,String> deptDepts = new HashMap<String,String>();
				for(JSONObject jsonObject:list){
					String assignDeptId = jsonObject.getString("ASSIGN_DEPT_ID");
					String assignUserName = jsonObject.getString("ASSIGN_USER_NAME");
					String taskNames = jsonObject.getString("TASK_NAMES");
					int taskCount = jsonObject.getIntValue("TASK_COUNT");
					StringBuffer sb = null;
					if(deptDepts.containsKey(assignDeptId)) {
						sb = new StringBuffer(deptDepts.get(assignDeptId));
					}else {
						sb = new StringBuffer("请查看以下待办任务名单。如果您的名字不在列表中或您的待办任务较少，请尽快报告您的任务情况。\n");
					}
					sb.append(assignUserName+"("+taskCount+")"+"："+taskNames);
					sb.append("\n");
					deptDepts.put(assignDeptId, sb.toString());
				}
				
				Set<String> deptIds = deptDepts.keySet();
				for(String deptId:deptIds) {
					String message = deptDepts.get(deptId);
					String deptName =  this.getMainQuery().queryForString("select t1.dept_name from easi_dept t1 where t1.dept_id = ?",new Object[]{deptId});
					if(StringUtils.notBlank(deptName)&&(deptName.contains("创新")||deptName.contains("开发")||deptName.contains("分部")||deptName.contains("研究")||deptName.contains("UI"))) {
						this.getLogger().info(deptId+">>deptWxMsg>>"+message);
						WeChatWebhookSender.sendTextMessage("dept@"+deptId,message);
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
	
	public void checkTask(){
		//未验收提醒
		try {
			EasyQuery query=getQuery();
			query.setMaxRow(200);
			List<JSONObject> list=query.queryForList("select *,count(1) task_count,group_concat(task_name separator '<br>') task_names from yq_task where task_state = 30 GROUP BY CREATOR order by finish_time desc",new Object[]{},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("CREATOR");
					String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
					String taskName=jsonObject.getString("TASK_NAME");
					String taskNames = jsonObject.getString("TASK_NAMES");
					
					int taskCount = jsonObject.getIntValue("TASK_COUNT");
					
					MessageModel model=new MessageModel();
					model.setFkId(jsonObject.getString("TASK_ID"));
					model.setReceiver(creator);
					model.setSender(assignUserId);
					
					
					if(taskCount>1) {
						model.setTitle("任务未验收提醒("+taskCount+")");
						model.setDesc(taskNames);
						jsonObject.put("TASK_NAME",taskNames);
						jsonObject.put("TASK_DESC","");
					}else {
						model.setTitle("未验收|"+taskName);
						model.setDesc(jsonObject.getString("TASK_DESC"));
					}
					jsonObject.put("NOTICE_NAME","请及时验收任务");
					model.setData(jsonObject);
					model.setTplName("noticeTask.html");
					WxMsgService.getService().sendCheckTaskMsg(model);
					try {
						WebSocketUtils.sendMessage(new MsgModel(creator, "请及时验收任务", taskName));
						EmailService.getService().sendTaskEmail(model);
					} catch (Exception e) {
						this.getLogger().error(null,e);
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
	public void createTask(){
		if(!KqNoticeService.getService().kqFlag()) {
			return;
		}
		//填写任务提醒
		try {
			EasyQuery query=getQuery();
			query.setMaxRow(200);
			EasySQL sql  = new EasySQL();
			sql.append("select IFNULL(t4.count,0) task_count,t1.user_id,t1.username from "+Constants.DS_MAIN_NAME+".easi_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_role_user t2 on t1.USER_ID = t2.USER_ID ");
			sql.append("LEFT JOIN(select t3.assign_user_id,count(1) count from yq_task t3 where ");
			sql.append(DateUtils.getBeforeDate(7, "yyyy-MM-dd"),"(plan_started_at >= ? ");
			sql.append(DateUtils.getBeforeDate(7, "yyyy-MM-dd"),"OR deadline_at >= ?)");
			sql.append("group by assign_user_id");
			sql.append(") t4 on t4.assign_user_id = t1.USER_ID");
			sql.append("where t1.STATE= 0 and t2.ROLE_ID='Developer' and t4.count is null");
			
			List<JSONObject> list=query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String assignUserId=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(assignUserId);
					model.setTitle("任务填写提醒");
					WxMsgService.getService().sendCreateTaskMsg(model);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
	
	
	public void affairNoticeToObj(String affairId,String objId) {
		try {
			//微信推送
			JSONObject row = this.getQuery().queryForRow("select t2.obj_id,t1.creator,t1.share_ids,t1.affair_name,t1.create_name,t2.user_id,t2.user_name,t1.affair_desc from yq_affair t1,yq_affair_obj t2 where t1.affair_id = t2.affair_id and t1.affair_id = ? and t1.affair_state >=10 and t2.obj_id = ?", new Object[] {affairId,objId},new JSONMapperImpl());
			if(row!=null) {
				MessageModel model = new MessageModel();
				String createName = row.getString("CREATE_NAME");
				String affairName = row.getString("AFFAIR_NAME");
				String affairDesc = row.getString("AFFAIR_DESC");
				model.setFkId(affairId);
				model.setTitle(affairName);
				model.setSender(row.getString("CREATOR"));
				model.setReceiver(row.getString("USER_ID"));
				model.setData1(createName+"-"+affairName);
				model.setData2(EasyDate.getCurrentDateString());
				affairDesc = Jsoup.clean(affairDesc, Whitelist.none());
				if(affairDesc.length()>150) {
					affairDesc = affairDesc.substring(0,150);
				}
				model.setDesc(affairDesc);
				WxMsgService.getService().sendAffairNoticeMsg(model);
				
				//发送邮件
				model.setDesc(row.getString("AFFAIR_DESC").replaceAll("/yq-work/", "https://work.yunqu-info.cn/yq-work/"));
				Map<String,Object> map=new HashMap<String, Object>();
				map.putAll(BeanMapUtilByReflect.beanToMap(model));
				
				String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("affairNotice.html").renderToString(map);
				model.setDesc(bodyHtml);
				EmailService.getService().sendEmail(model);
				
				//更新状态
				this.getQuery().executeUpdate("update yq_affair_obj set state = 1,send_time = ? where obj_id = ?",EasyDate.getCurrentDateString(),objId);
	
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
		}
	}
	
			
	public void affairNotice(String affairId) {
		try {
			String descText = "";
			//微信推送
			List<JSONObject> list = this.getQuery().queryForList("select t2.obj_id,t1.creator,t1.share_ids,t1.affair_name,t1.create_name,t2.user_id,t2.user_name,t1.affair_desc from yq_affair t1,yq_affair_obj t2 where t1.affair_id = t2.affair_id and t1.affair_id = ? and t1.affair_state >=10 and t2.state = 0", new Object[] {affairId},new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				MessageModel model = new MessageModel();
				String[] receiverUserIds = new String[list.size()];
				int i = 0 ;
				for(JSONObject row:list) {
					String creator = row.getString("CREATOR");
					String createName = row.getString("CREATE_NAME");
					String affairName = row.getString("AFFAIR_NAME");
					if(affairName.contains("AI")) {
						model.setEmailSender("DeepSeek");
					}
					String affairDesc = row.getString("AFFAIR_DESC");
					model.setTitle(affairName);
					model.setFkId(affairId);
					model.setSender(creator);
					model.setReceiver(row.getString("USER_ID"));
					model.setData1(createName+"-"+affairName);
					model.setData2(EasyDate.getCurrentDateString());
					affairDesc = Jsoup.clean(affairDesc, Whitelist.none());
					if(affairDesc.length()>150) {
						affairDesc = affairDesc.substring(0,150);
					}
					model.setDesc(affairDesc);
					descText = affairDesc;
					WxMsgService.getService().sendAffairNoticeMsg(model);
					
					receiverUserIds[i] = model.getReceiver();
					model.setData3(row.getString("SHARE_IDS"));
					model.setData4(row.getString("AFFAIR_DESC"));
					i++;
				}
				
				//发送邮件
				String shareIds = model.getData3();
				if(StringUtils.notBlank(shareIds)) {
					shareIds = model.getSender()+","+shareIds;
					model.setCc(shareIds.split(","));
				}else {
					model.setCc(new String[]{model.getSender()});
				}
				model.setReceivers(receiverUserIds);
				model.setDesc(model.getData4().replaceAll("/yq-work/", "https://work.yunqu-info.cn/yq-work/"));
				Map<String,Object> map=new HashMap<String, Object>();
				map.putAll(BeanMapUtilByReflect.beanToMap(model));
				map.put("userData",BeanMapUtilByReflect.beanToMap(StaffService.getService().getStaffInfo(model.getSender())));
				model.setFileList(CommonService.getService().getFileList(affairId));
				
				String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("affairNotice.html").renderToString(map);
				model.setDesc(bodyHtml);
				EmailService.getService().sendEmail(model);
				
				WeChatWebhookSender.sendHtmlMessage(WebhookKey.TEST, model.getTitle()+"\n"+descText);
				
				//更新状态
				this.getQuery().executeUpdate("update yq_affair_obj set state = 1,send_time = ? where affair_id = ?",EasyDate.getCurrentDateString(),affairId);
				this.getQuery().executeUpdate("update yq_affair set affair_state = 20,excute_send_time = ? where affair_id = ?",EasyDate.getCurrentDateString(),affairId);
			}
			
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
		}
	}
	
	public void affairSpecifiedTmNotice() {
		try {
			List<JSONObject> list = this.getQuery().queryForList("select affair_id from yq_affair where send_type = 10 and affair_state = 10 and excute_time <= ?", new Object[] {EasyDate.getCurrentDateString()}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject row:list) {
					this.affairNotice(row.getString("AFFAIR_ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
		}
	}
	
	public void affairMonthOfDayNotice() {
		Calendar calendar=Calendar.getInstance();
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		try {
			List<JSONObject> list = this.getQuery().queryForList("select affair_id from yq_affair where send_type = 20 and affair_state = 10 and month_days = ?", new Object[] {day}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject row:list) {
					this.affairNotice(row.getString("AFFAIR_ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
		}
	}
	
	
	

}
