package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.model.rowmapper.ProjectDomain;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.ProjectService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;
@WebServlet("/servlet/project/*")
public class ProjectServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	public EasyResult actionForGetProjectInfo() {
		JSONObject params = this.getJSONObject();
		String projectId = params.getString("projectId");
		try {
			JSONObject row = this.getQuery().queryForRow("select * from yq_project where PROJECT_ID = ?", new Object[] {projectId},new JSONMapperImpl());
			return EasyResult.ok(row);
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateProjectInfo(){
		String sql="select PROJECT_ID,PROJECT_NAME from yq_project";
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql);
			for(EasyRow row:list){
				String projectId = row.getColumnValue("PROJECT_ID");
				String projectName = row.getColumnValue("PROJECT_NAME");
				 if(projectName.length()>13){
					 String[] array = projectName.split(" ");
					 if(array[0].length()==13&&array[0].startsWith("20")){
						 projectName=projectName.substring(14);
					 }
				 }
				 this.getQuery().executeUpdate("update yq_project set PROJECT_NAME = ? where PROJECT_ID = ?",projectName, projectId);
				 this.getQuery().executeUpdate("update yq_project_contract set CONTRACT_NAME = ? where CONTRACT_ID = ?",projectName, projectId);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateProjectTeam(){
		String id = getJsonPara("projectId");
		if(StringUtils.isBlank(id)) {
			ProjectService.getService().updateAllTeams();
			return EasyResult.ok();
		}
		return ProjectService.getService().updateProjectTeam(id);
	}
	
	public EasyResult actionForAdd(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		try {
			//判断非0的合同号是否存在
			String projectNo=model.getString("PROJECT_NO");
			if(!"0".equals(projectNo)){
				boolean flag = this.getQuery().queryForExist("select count(1) from yq_project where PROJECT_NO = ?", projectNo);
				if(flag){
					return EasyResult.fail("合同号已存在.");
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		model.setCreator(getUserPrincipal().getUserId());
		model.addCreateTime();
		model.setProjectState(11);
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		try {
			 model.save();
			 EasyRecord module = new EasyRecord("yq_project_module","module_id");
			 module.set("module_id", model.getPrimaryValue());
			 module.set("project_id", model.getPrimaryValue());
			 module.set("module_name", "默认模块");
			 module.set("update_time", EasyDate.getCurrentDateString());
			 module.set("update_by", getUserId());
			 module.set("update_by_name", getUserName());
			 this.getQuery().save(module);
			 
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		JSONObject params = getJSONObject();
		String logInfoStr = params.getString("logInfoStr");
		ProjectModel model = getModel(ProjectModel.class, "project");
		model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		String projectId = model.getString("PROJECT_ID");
		try {
			if(!hasRole("PROJECT_MANAGER")) {
//				model.remove("IS_PROJECT_AWARD");
//				model.remove("PROJECT_AWARD_REASON");
			}
			model.update();
			if(StringUtils.notBlank(logInfoStr)){
				this.sendWxMsg("项目修改",getUserName()+"修改【"+model.getString("PROJECT_NAME")+"】项目信息");
				ProjectService projectService = new ProjectService(getRequest());
				ProjectDomain domain = ProjectService.getService().getProject(projectId);
				WeChatWebhookSender.sendMarkdownMessage(domain.getWebhook(),"项目修改",getUserName()+"修改【"+model.getString("PROJECT_NAME")+"】项目信息");
				projectService.addProjectLog(getRequest(),projectId,"",logInfoStr);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateTeam(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		record.setColumns(getJSONObject());
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateModule(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_MODULE","MODULE_ID");
		try {
			record.setColumns(getJSONObject());
			record.set("UPDATE_BY", getUserId());
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			if(StringUtils.isBlank(record.getString("MODULE_NAME"))){
				return EasyResult.fail("模块名不能为空.");
			}
			if(StringUtils.notBlank(record.getString("MODULE_ID"))){
				this.getQuery().update(record);
			}else{
				record.setPrimaryValues(RandomKit.randomStr());
				this.getQuery().save(record);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
	public EasyResult actionForAddFavorite(){
		EasyRecord record=new EasyRecord("YQ_FAVORITE","FAVORITE_ID");
		try {
			String fkId=getJsonPara("fkId");
			String sql="select count(1) from YQ_FAVORITE where fk_id = ? and favorite_by = ?";
			int count=this.getQuery().queryForInt(sql, fkId,getUserId());
			if(count>0){
				return EasyResult.fail("不能重复关注!");
			}
			record.setPrimaryValues(RandomKit.uuid());
			record.set("fk_id", fkId);
			record.set("favorite_by", getUserId());
			record.set("favorite_time",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelFavorite(){
		try {
			String favoriteId=getJsonPara("favoriteId");
			this.getQuery().executeUpdate("delete from YQ_FAVORITE  where fk_id = ? and favorite_by = ?",favoriteId,getUserId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForTransferProject(){
		try {
			String projectId=getJsonPara("projectId");
			String newProjectId=getJsonPara("newProjectId");
			this.getQuery().executeUpdate("update YQ_TASK set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_look_log set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_comments set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_oplog set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_version set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_weekly set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_weekly_project set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_work_hour set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_ops_version set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_link set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_kanban set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_folder set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_files set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_module set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_follow_record set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_task_group set project_id = ?  where project_id = ?",newProjectId,projectId);
//			this.getQuery().executeUpdate("update yq_project_team set project_id = ?  where project_id = ?",newProjectId,projectId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelProject(){
		try {
			String projectId=getJsonPara("projectId");
			this.getQuery().executeUpdate("update yq_project set is_delete =1  where project_id = ?",projectId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserPrincipal().getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	
	public EasyResult actionForUpdateProjectState(){
		JSONObject jsonObject = getJSONObject();
		String projectState = jsonObject.getString("projectState");
		String followState = jsonObject.getString("followState");
		String stakeholderId = jsonObject.getString("stakeholderId");
		String projectId = jsonObject.getString("projectId");
		EasyRecord model=new EasyRecord("yq_project_follow_record","follow_id");
		String id = FlowService.getService().getID();
		model.set("follow_id", id);
		model.set("update_by",getUserPrincipal().getUserId());
		model.set("create_name",getUserPrincipal().getUserName());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.set("is_public", jsonObject.getIntValue("isPublic"));
		model.set("project_id", projectId);
		if(StringUtils.notBlank(followState)) {
			model.set("feedback_type", followState);
		}
		JSONObject projectInfo = ProjectService.getService().getProjectInfo(projectId);
		if(projectInfo!=null) {
			model.set("project_name", projectInfo.getString("PROJECT_NAME"));
		}
		String ccIds = jsonObject.getString("ccIds");
		String ccNames = jsonObject.getString("ccNames");
		model.set("follow_date", jsonObject.getString("followDate"));
		model.set("stakeholder", jsonObject.getString("stakeholder"));
		model.set("effect_progress", jsonObject.getString("effectProgress"));
		model.set("has_dev_work", jsonObject.getString("hasDevWork"));
		model.set("problem_by", jsonObject.getString("problemBy"));
		model.set("stakeholder_id", stakeholderId);
		model.set("cc_names", ccNames);
		String feedbackContent = jsonObject.getString("feedbackContent");
		model.set("feedback", feedbackContent);
		model.set("level", jsonObject.getString("level"));
		if(StringUtils.notBlank(stakeholderId)) {
			model.set("follow_state", 1);
		}else {
			model.set("follow_state", 0);
		}
		try {
			if(StringUtils.notBlank(projectState)) {
				this.getQuery().executeUpdate("update YQ_PROJECT set PROJECT_STATE = ? ,UPDATE_TIME = ?,STATE_UPDATE_DESC = ?,LAST_FEEDBACK_TIME = ? where project_id  = ?",jsonObject.getString("projectState"),EasyDate.getCurrentDateString(),feedbackContent,EasyDate.getCurrentDateString(),jsonObject.getString("projectId"));
			}
			if(StringUtils.notBlank(jsonObject.getString("problemBy"))) {
				this.getQuery().executeUpdate("update YQ_PROJECT set problem_by = ?,LAST_FEEDBACK_TIME = ? where project_id  = ?",jsonObject.getString("problemBy"),EasyDate.getCurrentDateString(),jsonObject.getString("projectId"));
			}
			this.getQuery().save(model);
			if(StringUtils.notBlank(stakeholderId)) {
				MessageModel messageModel = new MessageModel();
				messageModel.setReceiver(stakeholderId);
				messageModel.setSender(getUserId());
				messageModel.setTitle("项目反馈-请尽快回复");
				messageModel.setUrl("/yq-work/project/"+projectId+"#反馈");
				messageModel.setDesc(feedbackContent);
				messageModel.setData3(getUserName());
				messageModel.setData4(messageModel.getTitle());
				if(StringUtils.notBlank(ccIds)) {
					messageModel.setCc(ccIds.split(","));
				}
				this.sendWxMsg(messageModel.getTitle(),messageModel.getDesc());
				WxMsgService.getService().sendCommentTaskMsg(messageModel);
				EmailService.getService().sendEmail(messageModel);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(id);
	}
	
	
	public EasyResult actionForDelProjectWorkHour(){
		JSONObject params = getJSONObject();
		EasyRecord record=new EasyRecord("yq_project_work_hour","item_id");
		record.setPrimaryValues(params.getString("itemId"));
		try {
			this.getQuery().deleteById(record);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddProjectWorkHour(){
		JSONObject params = getJSONObject();
		
		String monthId=  params.getString("monthId");
		Object obj = params.get("itemId");
		
		JSONArray array = new JSONArray();
		if(obj instanceof String) {
			array.add(obj.toString());
		}else {
			array = params.getJSONArray("itemId");
		}
		String whId= monthId+"_"+getUserId();
		
		for(int i=0;i<array.size();i++){
			String itemId = array.getString(i);
			boolean isAdd = true;
			if(itemId.length()<6) {
				isAdd = false;
				whId = params.getString("whId_"+itemId);
			}
			EasyRecord record=new EasyRecord("yq_project_work_hour","item_id");
			record.set("wh_id",whId);
			String projectId = params.getString("projectId_"+itemId);
			record.set("work_result",params.getString("result_"+itemId));
			record.set("project_id",projectId);
			record.set("work_time",params.getString("time_"+itemId));
			record.set("resident",params.getString("resident_"+itemId));
			record.set("project_name",params.getString("projectName_"+itemId));
			record.set("update_time",EasyDate.getCurrentDateString());
			record.set("update_by",getUserName());
			try {
				if(isAdd) {
					record.set("worker",getUserId());
					record.set("year",monthId.substring(0, 4));
					record.set("month_id",monthId);
					record.set("create_time",EasyDate.getCurrentDateString());
					record.set("creator",getUserId());
					record.set("create_name",getUserName());
					record.set("dept_id",getDeptId());
					record.set("dept_name",getDeptName());
					this.getQuery().save(record);
					ProjectService.getService().updateProjectTime(projectId);
				}else {
					record.set("item_id",itemId);
					this.getQuery().update(record);
					ProjectService.getService().updateProjectTime(projectId);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		
		EasySQL sql = new EasySQL("update yq_project_wh t1 set");
		sql.append("t1.project_num = (select count(DISTINCT(t2.project_id)) from yq_project_work_hour t2 where t2.wh_id = t1.id),");
		sql.append(EasyDate.getCurrentDateString(),"t1.update_time = ?,");
		sql.append(1,"t1.state = ?,");
		sql.append("t1.work_time = (select sum(t3.work_time) from yq_project_work_hour t3 where t3.wh_id = t1.id)");
		sql.append(whId,"where t1.id = ?");
		
		
		try {
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			this.getQuery().executeUpdate("UPDATE yq_project_wh t1 JOIN( SELECT wh_id, COUNT(DISTINCT project_id) as project_count, SUM(work_time) as total_work_time FROM yq_project_work_hour GROUP BY wh_id) t2 ON t1.id = t2.wh_id SET t1.project_num = t2.project_count, t1.work_time = t2.total_work_time where t1.month_id = ?", monthId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return EasyResult.ok();
	}
	
	public void actionForGoMindoc(){
		String projectId = getPara("projectId");
		try {
			String projectNo = this.getQuery().queryForString("select PROJECT_NO from yq_project where PROJECT_ID = ?", projectId);
			String id = this.getMainQuery().queryForString("select identify from mindoc_db.md_books where identify = ?", projectNo);
			if(StringUtils.isBlank(id)) {
				renderHtml("不存在,请先创建");
				return;
			}
			String memberId = this.getMainQuery().queryForString("select member_id from mindoc_db.md_members where account = ?", getRequest().getRemoteUser());
			if(StringUtils.isBlank(memberId)) {
				renderHtml("关联账号没注册");
				return;
			}
			String bookId = this.getMainQuery().queryForString("select book_id from mindoc_db.md_books where identify = ?", id);
			if(!this.getMainQuery().queryForExist("select count(1) from mindoc_db.md_relationship where member_id = ? and book_id = ?", memberId,bookId)) {
				EasyRecord record = new EasyRecord("mindoc_db.md_relationship");
				record.set("member_id",memberId);
				record.set("book_id",bookId);
				record.set("role_id",3);// 0 创建人 1 管理员 2 编辑者 3 观察者
				this.getMainQuery().save(record);
			}
			redirect("/yq-work/auth/mindoc?url=/docs/"+id);
		} catch (SQLException e) {
			renderHtml(e.getMessage());
		}
	}
	
	public EasyResult actionForSynToMindoc(){
		JSONObject jsonObject=getJSONObject();
		String projectId = jsonObject.getString("projectId");
		try {
			JSONObject row = this.getQuery().queryForRow("select PROJECT_ID,PROJECT_NAME,PROJECT_NO from yq_project where PROJECT_ID = ?", new Object[] {projectId}, new JSONMapperImpl());
			String projectNo = row.getString("PROJECT_NO");
			
			if(this.getMainQuery().queryForExist("select count(1) from mindoc_db.md_books where identify = ?", projectNo)) {
				return EasyResult.fail("已创建");
			}
			EasyRecord record  = new EasyRecord("mindoc_db.md_books","book_id");
			record.set("item_id", 1);
			record.set("privately_owned", 1);
			record.set("history_count", 10);
			record.set("auto_save", 1);
			record.set("is_use_first_document", 1);
			record.set("member_id", 1);
			record.set("is_download", 1);
			record.set("publisher", "云趣科技");
			record.set("description",getUserName()+"于"+EasyDate.getCurrentDateString()+"创建");
//			record.set("editor", "new_html");
			record.set("editor", "markdown");
			record.set("comment_status", "open");
			record.set("book_name",row.getString("PROJECT_NAME"));
			record.set("identify",projectNo);
			record.set("cover","/static/images/book.jpg");
			record.set("create_time",EasyDate.getCurrentDateString());
			this.getMainQuery().save(record);
			
			String bookId = this.getMainQuery().queryForString("select book_id from mindoc_db.md_books where identify = ?", projectNo);
			EasyRecord record2  = new EasyRecord("mindoc_db.md_relationship");
			record2.set("book_id",bookId);
			record2.set("role_id",0);
			record2.set("member_id",1);
			this.getMainQuery().save(record2);
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void sendWxMsg(String title,String msg) {
    	WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PROJECT,title,msg);
    }
}




