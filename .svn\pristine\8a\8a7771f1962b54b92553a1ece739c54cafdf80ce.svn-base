package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.rowmapper.ProjectDomain;
import com.yunqu.work.model.rowmapper.ProjectRowMapper;
import com.yunqu.work.utils.DateUtils;

import eu.bitwalker.useragentutils.DeviceType;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;

public class ProjectService extends AppBaseService {
	
	private static class Holder{
		private static ProjectService service = new ProjectService();
	}
	
	public static ProjectService getService(){
		return Holder.service;
	}
	
	public ProjectService(){
		
	}
	
	public ProjectService(HttpServletRequest request){
		this.request = request;
	}
	
	
	private boolean existTeamPerson(String projectId,String userId) {
		boolean result = false;
		try {
			result = this.getQuery().queryForExist("select count(1) from YQ_PROJECT_TEAM where PROJECT_ID = ? and USER_ID = ?", projectId,userId);
		} catch (SQLException e) {
			getLogger().error(e.getMessage(), e);
		}
		return result;
	}
	
	private boolean isPo(JSONObject projectInfo,String userId) {
		String projectPo = "";
		projectPo = projectInfo.getString("PO");
		projectPo = projectPo + projectInfo.getString("PROJECT_PO");
		return projectPo.indexOf(userId)>-1;
	}
	
	public EasyResult updateProjectTeam(String id){
		this.getLogger().info("updateProjectTeam>"+id);
		JSONObject projectInfo = null;
		try {
			projectInfo = this.getQuery().queryForRow("select * from yq_project where project_id = ?", new Object[] {id}, new JSONMapperImpl());
		} catch (SQLException ex) {
			getLogger().error(ex.getMessage(), ex);
		}
		if(projectInfo==null) {
			return EasyResult.fail();
		}
		String sql = "select DISTINCT(t1.ASSIGN_USER_ID) from YQ_TASK t1  where t1.PROJECT_ID = ?";
		List<JSONObject> list0 = null;
		try {
			list0 = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
			for(JSONObject json:list0) {
				String userId = json.getString("ASSIGN_USER_ID");
				StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
				if(staffModel!=null&&staffModel.isNormal()) {
					EasyRecord record=new  EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
					record.set("USER_ID", userId);
					record.set("PROJECT_ID", id);
					if(isPo(projectInfo,userId)) {
						record.set("ADMIN_FLAG", 1);
					}
					try {
						if(!existTeamPerson(id,userId)) {
							record.set("JOIN_TIME", EasyDate.getCurrentDateString());
							this.getQuery().save(record);
						}else if(isPo(projectInfo,userId)){
							this.getQuery().update(record);
						}
					} catch (Exception e) {
						getLogger().error(null, e);
					}
				}
			}
		} catch (SQLException ex) {
			getLogger().error(null, ex);
		}
		sql = "select DISTINCT(t1.creator) from YQ_TASK t1  where t1.PROJECT_ID = ?";
		try {
			List<JSONObject> list3 = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
			for(JSONObject json:list3) {
				String userId = json.getString("CREATOR");
				StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
				if(staffModel!=null&&staffModel.isNormal()) {
					EasyRecord record=new  EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
					record.set("USER_ID", userId);
					record.set("PROJECT_ID", id);
					if(isPo(projectInfo,userId)) {
						record.set("ADMIN_FLAG", 1);
					}
					try {
						if(!existTeamPerson(id,userId)) {
							record.set("JOIN_TIME", EasyDate.getCurrentDateString());
							this.getQuery().save(record);
						}else if(isPo(projectInfo,userId)){
							this.getQuery().update(record);
						}
					} catch (Exception e) {
						getLogger().error(null, e);
					}
				}
		 }
//		sql = "select DISTINCT(t2.user_id) from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id where t1.project_id = ?";
//		List<JSONObject> list = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
//		for(JSONObject json:list) {
//			String userId = json.getString("USER_ID");
//			EasyRecord record=new  EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
//			record.set("USER_ID", userId);
//			record.set("PROJECT_ID", id);
//			try {
//				if(!existTeamPerson(id,userId)) {
//			        record.set("JOIN_TIME", EasyDate.getCurrentDateString());
//					this.getQuery().save(record);
//				}else if(isPo(projectInfo,userId)){
//					this.getQuery().update(record);
//				}
//			} catch (Exception e) {
//				getLogger().error(null, e);
//			}
//		}
		sql="select DISTINCT(t1.creator) from yq_weekly t1 INNER JOIN yq_weekly_project t2 on t2.weekly_id = t1.weekly_id where t2.project_id = ?";
		List<JSONObject> list2 = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
		for(JSONObject json:list2) {
			String userId = json.getString("CREATOR");
			EasyRecord record=new  EasyRecord("yq_project_team","PROJECT_ID","USER_ID");
			record.set("USER_ID", userId);
			record.set("PROJECT_ID", id);
			if(isPo(projectInfo,userId)) {
				record.set("ADMIN_FLAG", 1);
			}
			try {
				if(!existTeamPerson(id,userId)) {
					record.set("JOIN_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}else if(isPo(projectInfo,userId)){
					this.getQuery().update(record);
				}
			} catch (Exception e) {
				getLogger().error(null, e);
			}
		}
	} catch (SQLException e) {
		getLogger().error(null, e);
	}
	return EasyResult.ok();
 }
	
	public void updateProjectTime(String projectId) {
		try {
			this.getQuery().executeUpdate("update yq_project set update_time = ? where PROJECT_ID = ?", EasyDate.getCurrentDateString(),projectId);
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	public void updateAllTeams() {
		try {
			List<JSONObject> list = this.getQuery().queryForList("select PROJECT_ID from yq_project where update_time > ?", new Object[] {DateUtils.getDate(-60*24*7)}, new JSONMapperImpl());
			for(JSONObject jsonObject:list) {
				updateProjectTeam(jsonObject.getString("PROJECT_ID"));
			}
			this.getQuery().executeUpdate("UPDATE yq_project t1 JOIN ( SELECT t2.project_id, COUNT(1) AS person_count FROM yq_project_team t2 JOIN yq_main.easi_user t3 ON t3.USER_ID = t2.USER_ID WHERE t3.STATE = 0 GROUP BY t2.project_id ) AS sub ON t1.project_id = sub.project_id SET t1.person_count = sub.person_count");
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	public void updateProjectWorktime() {
		try {
			this.getQuery().executeUpdate("UPDATE yq_project t1 LEFT JOIN ( SELECT project_id, SUM(work_time) AS total_work_time FROM yq_project_work_hour GROUP BY project_id ) AS t2 ON t1.project_id = t2.project_id SET t1.po_ph = IFNULL(t2.total_work_time, 0)");
			this.getQuery().executeUpdate("UPDATE yq_project t1 LEFT JOIN ( SELECT t2.project_id, SUM(IFNULL(t2.work_day, 0)) AS total_work_day FROM yq_weekly_project t2 JOIN yq_weekly t3 ON t3.weekly_id = t2.weekly_id WHERE t3.dept_name NOT LIKE '%工程%' GROUP BY t2.project_id ) AS sub ON t1.project_id = sub.project_id SET t1.dev_ph = IFNULL(sub.total_work_day, 0)");
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	public JSONObject getProjectInfo(String projectId) {
		JSONObject row = null;
		try {
			row = this.getQuery().queryForRow("select * from yq_project where PROJECT_ID = ?", new Object[] {projectId}, new JSONMapperImpl());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
		return row;
	}
	
	public ProjectDomain getProject(String projectId) {
		ProjectDomain row = null;
		try {
			row = this.getQuery().queryForRow("select * from yq_project where PROJECT_ID = ?", new Object[] {projectId}, new ProjectRowMapper());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
		return row;
	}
	
	public void addProjectLog(HttpServletRequest request,String projectId,String businessId,String content) {
		String id = RandomKit.uniqueStr();
		JSONObject data = JsonKit.getJSONObject(request, null);
		this.getLogger().info("addProjectLog>"+id+">"+data.toJSONString());
		UserPrincipal principal = getUserPrincipal();
		EasyRecord record = new EasyRecord("YQ_PROJECT_OPLOG","LOG_ID");
		try {
			record.set("log_id", id);
			record.set("project_id", projectId);
			record.set("business_id", businessId);
			record.set("op_by", principal.getUserId());
			record.set("content",content);
			record.set("op_name", principal.getUserName());
			record.set("op_url", request.getRequestURL().toString());
			record.set("op_param", request.getQueryString());
			record.set("op_time", EasyDate.getCurrentDateString());
			this.setDevice(record);
			this.getQuery().save(record);
		} catch (Exception e) {
			getLogger().error(null, e);
		}
	}
	
	public void setDevice(EasyRecord record) {
		UserAgent ua = UserAgent.parseUserAgentString(getRequest().getHeader("User-Agent"));
		if(ua!=null) {
			OperatingSystem os = ua.getOperatingSystem();
			if(os!=null) {
				DeviceType deviceType = os.getDeviceType();
				if(deviceType!=null) {
					record.set("device",deviceType.toString());
				}
			}
		}
	}
}
