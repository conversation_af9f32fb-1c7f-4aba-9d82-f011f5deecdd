<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		#folderName a,span{color: #999;margin: 0px 3px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm" data-page-hide="true">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-file"></span> 全部文档</h5>
	          		     <div class="input-group input-group-sm" style="width: 200px;">
							 <span class="input-group-addon">文档名称</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="files"></table>					    
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.detail">详情</a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
  			<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="list.addFavorite">收藏</a>
  			{{if isSuperUser}}<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="list.hideDoc">屏蔽</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var currentUserId=getCurrentUserId();
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'DocDao.list',
					id:'files',
					skin:'line',
					page:true,
					limit:15,
					rowDoubleEvent:'list.detail',
					cols: [[
					 {type:'numbers',width:40,align:'center',title:'序号'},
		             {
					    field: 'TITLE',
						title: '文档库名称',
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							var efficient = row['EFFICIENT'];
							if(efficient=='1'){
								return '<span class="notEfficient">'+row.TITLE+'</span>';
							}
							return row.TITLE;
						}
					},{
						title: '',
						align:'center',
						width:200,
						templet:function(row){
							row['currentUserId']=currentUserId;
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar',row);
						}
					},{
					    field: 'VIEW_COUNT',
						title: '阅读量',
						align:'center',
						sort:true,
						width:85,
						templet:function(row){
							return row.VIEW_COUNT;
						}
					},{
					    field: 'FAVORITE_COUNT',
						title: '收藏数',
						align:'center',
						sort:true,
						width:85,
						templet:function(row){
							return row.FAVORITE_COUNT;
						}
					},{
					    field: 'MANAGER',
						title: '发布人',
						align:'right',
						width:100,
						templet:function(row){
							return getUserName(row.MANAGER);
						}
					}
					]],done:function(res){
						//$(".layui-table-header").css("display","none");
						$('.notEfficient').parent().parent().css('text-decoration','line-through');
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				//popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',title:'编辑',data:{docId:data.DOC_ID,folderId:data.FOLDER_ID}});
				window.open("${ctxPath}/docEdit/"+data.DOC_ID);
			},
			detail:function(data){
				//popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/doc/'+data.DOC_ID,title:'详情',data:{op:'detail',docId:data.DOC_ID,folderId:data.FOLDER_ID}});
				window.open('${ctxPath}/fly/docs/'+data.DOC_ID);
			},
			addFavorite:function(data,obj) {
				ajax.remoteCall("${ctxPath}/servlet/doc?action=addFavorite",{fkId:data.DOC_ID},function(result) { 
					if(result.state == 1){
						obj.update({FAVORITE_COUNT: parseInt(data.FAVORITE_COUNT)+1});
						layer.msg("收藏成功！",{icon:1,time:1200});
					}else{
						layer.msg(result.msg,{icon: 7,time:1200});
					}
				});
			},
			hideDoc:function(data){
				layer.prompt({title: '请输入要屏蔽的原因',formType: 2,value:'内容没价值,不适合共享.',icon:3},function(val){
					ajax.remoteCall("${ctxPath}/servlet/doc?action=update",{comment:val,'doc.DOC_ID':data.DOC_ID,'doc.DOC_AUTH':2},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>