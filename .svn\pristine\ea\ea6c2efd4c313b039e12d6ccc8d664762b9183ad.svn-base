<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		.header{
			line-height:40px;
			height: 40px;
			border-bottom: 1px solid #eee;
		}
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		.layui-tree-txt{color:#7b6c6c;font-size: 13px;margin-left: -6px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		#searchForm .btn .caret{color: #fff;}
		/* #searchForm .layui-table-page{display: none;} */
		.layui-tree-icon{height: 16px;width: 16px;line-height: 13px;}
	    .page-left{
	    	position:absolute;
	    	left:0;
	    	top:0;
	    	bottom:0;
			overflow:auto;
			width: 260px;
			float: left;
			background-color: #fff;
		}
		.page-right{
			position: absolute;
			right: 5px;
			top: 5px;
			bottom:0px;
			left: 265px;
		}
		@media screen and (max-width:768px){
			.page-right{width:calc(100% - 0px);margin-left:0px;}
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<div onclick="changeMode(this)" style="position: absolute;right: 40px;bottom: 30px;width: 60px;height: 60px;cursor: pointer;;line-height: 15px;padding: 16px;background-color: red;border-radius: 50%;font-size: 13px;z-index: 9999;color: #fff;">文档<br>模式</div>
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
			<input type="hidden" name="folderCode" id="folderCode" value="${param.folderCode}"/>
			<input type="hidden" name="folderId" id="folderId" value="${param.folderId}"/>
			<input type="hidden" name="folderAuth" id="folderAuth" value="${param.folderAuth}"/>
			<input type="hidden" id="folderName"/>
			<div class="ibox-content page-left layui-hide-xs">
				<div class="header">
					文件夹列表
					<div class="btn-group pull-right mt-10">
						<button type="button" class="btn btn-xs btn-warning" onclick="list.addFolder()">+ 新增目录</button>
						<button type="button" class="btn btn-xs btn-warning dropdown-toggle" data-toggle="dropdown">
							<span class="caret"></span>
						</button>
						<ul class="dropdown-menu" role="menu" style="left: -54px">
						    <li><a href="javascript:list.editFolder()">修改</a></li>
						    <li><a href="javascript:list.openFolder()">单独打开</a></li>
						    <li><a href="javascript:list.openDocs()">文档模式</a></li>
						</ul>
					</div>
				</div>
				<div id="tree"></div>
			</div>
			<div class="page-right">
				<div class="ibox">
					<div class="ibox-title layui-hide-xs clearfix">
		          		 <div class="form-group">
		          		     <div class="input-group input-group-sm" style="width: 220px">
								 <span class="input-group-addon">文档库名称</span>	
								 <input type="text" name="folderName" class="form-control input-sm">
						     </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
								 <div class="btn-group">
									<button type="button" class="btn btn-sm btn-info" onclick="list.addDoc()">+ 新增文档</button>
									<button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
										<span class="caret"></span>
									</button>
									<ul class="dropdown-menu" role="menu" style="left: -54px">
									    <li><a href="javascript:$('#localfile').click()">上传文件</a></li>
									</ul>
								</div>
							 </div>
		          	     </div>
		              </div> 
						<div class="ibox-content" style="min-height: calc(100vh - 100px);">
						    <table class="layui-hide" id="files"></table>
						</div>
					</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar2">
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}
			  <a class="layui-btn layui-btn-xs layui-hide-xs" lay-event="list.editDoc">编辑</a>
			 {{else}}
  			  <a class="layui-btn layui-btn-xs layui-btn-warm layui-hide-xs" lay-event="list.addFavorite">收藏</a>
			{{/if}}
		</script>
		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			loadTree();
			list.loadFile();
		});
		
		var folderObj = {};
		
		var uploadFile = function(callback){
			var id = $("#folderId").val();
			var folderName = $("#folderName").val()||'根目录';
			if(id=='0'||id==''){
				layer.msg('请选择目录');
				return;
			}
			layer.msg('上传至'+folderName,{time:800,icon:1},function(){
				$("#randomId").render({success:function(){
					var docId=$("#randomId").val();
					easyUploadFile({callback:'callback',fileMaxSize:(1024*500),fkId:docId,source:'doc'});
				}});
			});
		}
		
		var callback = function(data,params){
			var id=data.id;
			var url=data.url;
			var name=data.name;
			var docId=params.fkId;
			var data={'doc.DOC_ID':docId,'doc.TITLE':name,'doc.FOLDER_ID':$("#folderId").val(),'doc.DOC_DESC':'见附件'};
			ajax.remoteCall("${ctxPath}/servlet/doc?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function loadTree(){
			var folderCode = $("#folderCode").val();
			var folderAuth = $("#folderAuth").val();
			ajax.remoteCall("${ctxPath}/webcall?action=FolderDao.docFolder",{folderCode:folderCode,folderAuth:folderAuth},function(rs) { 
				var result= rs.data;
				var data = dataToTree(result,{idFiled: 'FOLDER_ID', textFiled: 'FOLDER_NAME', parentField: 'P_FOLDER_ID', childField: '',def:{spread:false}, map: {FOLDER_ID: 'id', FOLDER_NAME: 'title' } });
				if(folderAuth==1){
					data = [{title:'个人文档',spread:true,FOLDER_ID:'1',FOLDER_NAME:'个人可见',FOLDER_CODE:'',FOLDER_AUTH:1,children:data}];
				}else if(folderAuth==2){
					data = [{title:'部门文档',spread:true,FOLDER_ID:'2',FOLDER_NAME:'部门文档',FOLDER_CODE:'',FOLDER_AUTH:2,children:data}];
				}else if(folderAuth==3){
					data = [{title:'指定可见',spread:true,FOLDER_ID:'3',FOLDER_NAME:'指定可见',FOLDER_CODE:'',FOLDER_AUTH:3,children:data}];
				}
				layui.use('tree', function(){
				    var tree = layui.tree;
				    tree.render({
				        elem: '#tree',
				        data: data,
				        click: function(obj){
				        	var el = obj.elem;
				        	$('.layui-tree-txt').css({color:'#444444'});
				        	$(el).find('.layui-tree-txt').css({color:'#000000'});
				        
				        	var row = obj.data;
				           	var id = row['FOLDER_ID'];
				        	var name = row['FOLDER_NAME'];
				        	var auth = row['FOLDER_AUTH'];
				        	var folderCode = row['FOLDER_CODE'];
				        	
				        	$("#folderId").val(id);
				        	$("#folderCode").val(folderCode);
				        	$("#folderAuth").val(auth);
				           	$("#folderName").val(name);
				           	
				           	folderObj = row;
			           		
				           	list.query();
				        }
				   });
				});
				
				setTimeout(function(){
					$(".layui-icon-addition:eq(0)").click();
					$(".layui-tree-set .layui-icon-addition:eq(0)").click();
				},300);
				
			});
		}
		var list={
			loadFile:function(){
				var height = 'full-90';
				var device = layui.device();
			    if(device.mobile){
			    	height = 'full-50';
			    } 
				$("#searchForm").initTable({
					mars:'FolderDao.folderDocList',
					id:'files',
					page:true,
					limit:30,
					height:height,
					cols: [[
					 {type:'numbers',width:50,align:'center',title:'序号'},
		             {
					    field: 'TITLE',
						title: '文档库名称',
						align:'left',
						minWidth:180,
						event:'list.detail',
						style:'padding-left:0px;cursor:pointer;',
						templet:function(row){
							var efficient = row['EFFICIENT'];
							if(efficient=='1'){
								return '<span class="notEfficient">'+row.FOLDER_NAME+"."+row.TITLE+'</span>';
							}
							return row.TITLE+"<span class='f-12' style='color:#999;'>#"+row.FOLDER_NAME+"</span>";
						}
					},{
					    field: 'CREATE_NAME',
						title: '发布人',
						align:'center',
						width:80
					},{
						field:'VIEW_COUNT',
						title:'阅读量',
						align:'center',
						width:60
					},{
						field:'CREATE_TIME',
						align:'center',
						title:'发布时间',
						width:145
					},{
						title: '操作',
						align:'center',
						width:70,
						hide:device.mobile,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar2',row);
						}
					}
					]],done:function(res){
						$('.notEfficient').parent().parent().css('text-decoration','line-through');
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'files'});
			},
			editDoc:function(data){
				popup.layerShow({type:1,anim:0,scrollbar:false,full:true,url:'${ctxPath}/pages/doc/doc-edit.jsp',title:'编辑',data:{'doc.DOC_ID':data.DOC_ID,docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			addDoc:function(){
				var id = $("#folderId").val();
				var folderName = $("#folderName").val()||'根目录';
				if(id=='0'||id==''){
					layer.msg('请点击选择目录');
					return;
				}
				var folderAuth = $("#folderAuth").val()||'0';
				popup.layerShow({type:1,full:true,anim:0,scrollbar:false,area:['100%','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',data:{folderId:id,folderName:folderName,folderAuth:folderAuth},title:'新增文档'});
			},
			addFolder:function(){
				var folderName = $("#folderName").val();
				var folderAuth = $("#folderAuth").val()||'0';
				var pFolderId = $("#folderId").val();
				if(folderName){
					popup.layerShow({type:1,area:['450px','350px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{pFolderId:pFolderId,folderName:folderName,folderAuth:folderAuth,source:'0'}});
				}else{
					layer.msg('请先点击目录选择');
				}
			},
			editFolder:function(){
				var folderId = folderObj['FOLDER_ID'];
				if(folderId){
					var folderAuth = folderObj['FOLDER_AUTH'];
					var creator = folderObj['CREATOR'];
					var currentUserId = getCurrentUserId();
					if(creator!=currentUserId&&!isSuperUser){
						layer.msg('您不是创建人无权修改');
						return;
					}
					popup.layerShow({type:1,area:['450px','350px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'编辑文件夹',data:{folderId:folderId,folderAuth:folderAuth}});
				}
			},
			openFolder:function(){
				var folderName = folderObj['FOLDER_NAME'];
				var folderCode = folderObj['FOLDER_CODE'];
				if(folderName&&folderCode){
					var folderId = folderObj['FOLDER_ID'];
					var folderAuth = folderObj['FOLDER_AUTH'];
				    popup.openTab({id:'folderList',url:'/yq-work/folder/'+folderCode,title:folderName});
				}
			},
			openDocs:function(){
				var folderName = folderObj['FOLDER_NAME'];
				if(folderName){
					var folderCode = folderObj['FOLDER_CODE'];
				    window.open('/yq-work/docs/'+folderCode);
				}
			},
			detail:function(data){
				var docId = data['DOC_ID'];
				var docCode = data['DOC_CODE'];
				var folderId = data['FOLDER_ID'];
				window.open("/yq-work/doc/"+docCode);
				return;
				/* var url  = '${ctxPath}/pages/doc/doc-detail.jsp';
				top.popup.layerShow({type:2,maxmin:true,full:fullShow(),anim:0,scrollbar:false,offset:'20px',area:['70%','80%'],url:url,title:data['TITLE'],data:{op:'detail',isDiv:0,docId:docId,folderId:folderId},btn:['新标签卡打开','复制链接','关闭'],yes:function(){
					window.open("/yq-work/doc/"+docCode);
				},btn1:function(){
					window.open("/yq-work/doc/"+docCode);
				},btn2:function(){
					var textarea = document.createElement('textarea');
				    document.body.appendChild(textarea);
				    textarea.style.position = 'fixed';
				    textarea.style.clip = 'rect(0 0 0 0)';
				    textarea.style.top = '10px';
				    textarea.value = "http://work.yunqu-info.cn/yq-work/doc/"+docCode;
				    textarea.select();
				    document.execCommand('copy', true);
				    document.body.removeChild(textarea);
				    top.layer.alert('复制完成',{icon:1,offset:'80px',title:'复制成功'},function(index){
						top.layer.close(index);
					});
				}}); */
			},
			addFavorite:function(data) {
				ajax.remoteCall("${ctxPath}/servlet/doc?action=addFavorite",{fkId:data.DOC_ID},function(result) { 
					if(result.state == 1){
						layer.msg("收藏成功！",{icon:1,time:1200});
					}else{
						layer.msg(result.msg,{icon: 7,time:1200});
					}
				});
			}
		}
		
		function changeMode(){
			var folderName = folderObj['FOLDER_NAME'];
			if(folderName){
				var folderCode = $("#folderCode").val();
				popup.openTab({id:'folderList',url:'/yq-work/docs/'+folderCode,title:folderName});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>