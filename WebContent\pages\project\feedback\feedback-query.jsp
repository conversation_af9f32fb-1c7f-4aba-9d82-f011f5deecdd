<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>风险管理</title>
	<style>
		.layui-table-tool{background-color: #ffffff;}
		.layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <form  autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
    <input type="hidden" name="followState" id="followState" value=""/>
  	<div class="ibox-content">
		<div class="layui-tab layui-tab-brief task-tab" lay-filter="followTab" style="padding-top: 10px;">
			    <ul class="layui-tab-title" data-mars="ProjectRiskDao.riskCountStat">
			        <li data-state="" class="layui-this">全部 </li>
			        <li data-state="1">待回复</li>
			        <li data-state="9">已答复</li>
			    </ul>
			    <div class="layui-tab-content" style="padding: 0px;">
			        <div class="layui-tab-item layui-show"></div>
			        <div class="layui-tab-item"></div>
			        <div class="layui-tab-item"></div>
			    </div>
			    <div class="form-group mt-10">
					<div class="input-group input-group-sm ml-5"  style="width: 280px">
						 <span class="input-group-addon">反馈时间</span>	
						 <input type="text" name="beginDate" data-laydate="{type:'date'}" class="form-control input-sm">
						 <span class="input-group-addon">至</span>
						 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm">
					</div>	
				    <div class="input-group input-group-sm"  style="width: 280px">
						 <span class="input-group-addon">计划初验日期</span>	
						 <input style="width: 90px;display: inline-block" type="text" name="cyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm">
						 <input style="width: 90px;display: inline-block" type="text" name="cyEndDate" data-laydate="{type:'date'}" class="form-control input-sm">
				      </div>
				      <div class="input-group input-group-sm"  style="width: 280px">
						 <span class="input-group-addon">计划终验日期</span>	
						 <input style="width: 90px;display: inline-block" type="text" name="zyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm">
						 <input style="width: 90px;display: inline-block" type="text" name="zyEndDate" data-laydate="{type:'date'}" class="form-control input-sm">
				     </div>
			    	  <div class="input-group input-group-sm ml-5"  style="width: 150px">
						 <span class="input-group-addon">当前责任方</span>	
						 <select name="problemBy" class="form-control input-sm" onchange="reloadRisk();">
							<option value="">--</option>
							<option value="开发">开发</option>
							<option value="工程">工程</option>
							<option value="商务">商务</option>
							<option value="客户">客户</option>
							<option value="第三方">第三方</option>
						 </select>
				     </div>
			    </div>
		       <div class="form-group mt-10">
			    	  <div class="input-group input-group-sm ml-5"  style="width: 150px">
						 <span class="input-group-addon">影响进度</span>	
						 <select name="effectProgress" class="form-control input-sm">
							<option value="">--</option>
							<option value="0">是</option>
		  					<option value="1">否</option>
						 </select>
				     </div>
			    	  <div class="input-group input-group-sm ml-5"  style="width: 150px">
						 <span class="input-group-addon">开发工作量</span>	
						 <select name="hasDevWork" class="form-control input-sm">
							<option value="">--</option>
		  					<option value="0">是</option>
		  					<option value="1">否</option>
						</select>
				     </div>
				     <div class="input-group input-group-sm ml-5"  style="width: 200px">
						 <span class="input-group-addon">项目</span>	
						 <input type="hidden" name="projectId" class="form-control input-sm">
						 <input type="text" id="projectId" readonly="readonly" data-fn="reloadRisk" onclick="singleProject(this);" class="form-control input-sm">
				     </div>
				      <div class="input-group input-group-sm ml-5"  style="width: 130px">
						 <span class="input-group-addon">反馈人</span>	
						 <input type="text" name="creatorName" class="form-control input-sm">
				     </div>
			    	  <div class="input-group input-group-sm hidden"  style="width: 130px">
						 <span class="input-group-addon">答复人</span>	
						 <input type="text" name="ownerName" class="form-control input-sm">
				     </div>
				     <div class="input-group input-group-sm" style="width: 160px;">
						<span class="input-group-addon">反馈类别*</span>	
		  				<select class="form-control input-sm" name="followState" style="width: 120px;" onchange="reloadRisk(this);">
	                  		<option value="">--请选择--</option>
	                  		<option value="进度更新">进度更新</option>
							<option value="问题报告">问题报告</option>
							<option value="风险管理">风险管理</option>
							<option value="需求变更">需求变更</option>
							<option value="技术讨论">技术讨论</option>
							<option value="质量保证">质量保证</option>
							<option value="资源分配">资源分配</option>
							<option value="决策支持">决策支持</option>
							<option value="用户反馈">用户反馈</option>
							<option value="会议纪要">会议纪要</option>
							<option value="文档更新">文档更新</option>
							<option value="其他">其他</option>
	                   </select>
				     </div>
				     <div class="input-group input-group-sm">
						 <button type="button" class="btn btn-sm btn-default" onclick="reloadRisk()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					</div>
		       </div>
		 </div>
		 <table class="layui-hide" id="followList"></table>
  	  </div>
	</form>
	<script  type="text/html" id="riskToolBar">
    	<button class="btn btn-info btn-sm" lay-event="exportData" type="button">导出</button>
	</script>
</EasyTag:override>
<EasyTag:override name="script">
 <script type="text/javascript">

 	var projectId = '${param.projectId}';
    
    function loadFeedbackTable(){
        $("#searchForm").initTable({
            mars:'ProjectDao.projectFollowQuery',
            id:"followList",
            height:'full-110',
            limit:30,
            limits:[30,50,100,200,300,500],
            toolbar:'#riskToolBar',
            data:{},
            cols: [[
                {title:'序号',type:'numbers'},
                {
                    title: '操作',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return '<a class="btn btn-xs btn-link" href="javascript:;" lay-event="followDetail">详情</a>';
                    }
                },
                {
                    field: 'PROJECT_NO',
                    title: '合同编号',
                    align:'center',
                    width:110
                },{
                	title:'项目名称',
                	field:'PROJECT_NAME',
                	event:'projectDetailByRow',
                	minWidth:280
                },{
                    field: 'UPDATE_TIME',
                    title: '反馈时间',
                    align:'center',
                    width:140,
                },
                {
                    field: 'FEEDBACK_TYPE',
                    title: '反馈分类',
                    align:'center',
                    width:90
                },{
                    field: 'CREATE_NAME',
                    title: '发起人',
                    width:70,
                    align:'center'
                },{
    			    field: 'PROBLEM_BY',
    				title: '当前责任方',
    				width:80,
    				align:'center'
    			},{
                    field: 'FEEDBACK',
                    title: '反馈说明',
                    align:'left',
                    minWidth:300,
                    templet:function(row){
                    	return cutText(row.FEEDBACK,80);
                    }
                },{
				    field: 'CY_DATE',
					title: '计划初验',
					width:90,
					align:'center'
				},{
				    field: 'CY_REAL_DATE',
					title: '实际初验日期',
					width:120,
					align:'center'
				},{
				    field: 'ZY_DATE',
					title: '计划终验日期',
					width:120,
					align:'center'
				},{
				    field: 'ZY_REAL_DATE',
					title: '实际终验日期',
					width:120,
					align:'center'
				},{
    			    field: 'HAS_DEV_WORK',
    				title: '开发工作量',
    				width:80,
    				align:'center',
    				templet:function(row){
    					if(row['HAS_DEV_WORK']=='0'){
    						return '是';
    					}
    					if(row['HAS_DEV_WORK']=='1'){
    						return '否';
    					}
    					return row['HAS_DEV_WORK'];
    				}
    			},{
    			    field: 'EFFECT_PROGRESS',
    				title: '影响进度',
    				width:80,
    				align:'center',
    				templet:function(row){
    					if(row['EFFECT_PROGRESS']=='0'){
    						return '是';
    					}
    					if(row['EFFECT_PROGRESS']=='1'){
    						return '否';
    					}
    					return row['EFFECT_PROGRESS'];
    				}
    			},{
                    field:'STAKEHOLDER',
                    title:'答复人',
                    width:70,
                    hide:true
                },
                {
                    field: 'PO_NAME',
                    title: '开发负责人',
                    align:'center',
                    width:90
                },
                {
                    field: 'PROJECT_PO_NAME',
                    title: '项目经理',
                    align:'center',
                    width:90
                },
                {
                    field: 'SALES_BY_NAME',
                    title: '销售',
                    align:'center',
                    width:90
                },{
                    field: 'REPLY_CONTENT',
                    title: '答复内容',
                    align:'left',
                    minWidth:200,
                    hide:true,
                    templet:function(row){
                    	return cutText(row.REPLY_CONTENT,80);
                    }
                },{
                    field: 'LEVEL',
                    title: '紧急程度',
                    align:'center',
                    width:80
                },{
                    field: 'FOLLOW_STATE',
                    title: '处理状态',
                    align:'center',
                    hide:true,
                    width:80,
                    templet:function(row){
                        return followStateLabel(row.FOLLOW_STATE);
                    }
                }
            ]],done:function(result){
            	$("tbody [data-field='PROBLEM_BY']").each(function(){
            		var div = $(this).find('div');
            		if(div.text()){
            			$(this).css('background-color','#dbff00');
            		}
            	});
         }});
    }

    function  filterElement(){
       layui.element.on('tab(followTab)', function(elem){
           var state = $(this).data('state');
           $("#followState").val(state);
           reloadRisk();
       });
    }

    function reloadRisk(){
        $("#searchForm").queryData({id:'followList'});
    }

    function followStateLabel(state){
        var json = {0:'<label class="label label-danger label-outline">无需回复</label>',1:'<label class="label label-danger label-outline">待回复</label>',9:'<label class="label label-success">已完成</label>'};
        var result=json[state]||'';
        if(result){
            return result;
        }else{
            return '';
        }
    }

    function  followDetail(data){
    	var isAdmin = 0;
    	if(top.isAdmin=='1'||isSuperUser){
    		isAdmin = 1;
    	}
        var projectId = data['PROJECT_ID'];
        var followId = data['FOLLOW_ID'];
        var stakeholder = data['STAKEHOLDER'];
        if(stakeholder){
	        var json  = $.extend({},{businessId:followId});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
        }else{
        	window.open("/yq-work/project/"+projectId+"#反馈");
        }
    }
    
    function exportData(){
    	layer.msg('todo',{icon:7,offset:'20px',time:1200});
    }
    
    
    $(function(){
    	loadFeedbackTable();
    	filterElement();
    });

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
