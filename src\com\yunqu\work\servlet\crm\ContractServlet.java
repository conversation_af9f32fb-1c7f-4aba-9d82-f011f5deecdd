package com.yunqu.work.servlet.crm;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ProjecContractModel;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

@WebServlet("/servlet/projectContract/*")
public class ContractServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public void actionForSynData() {
        try {
            List<JSONObject> list = this.getQuery().queryForList("select * from YQ_PROJECT_CONTRACT", null, new JSONMapperImpl());
            ProjectModel model = new ProjectModel();
            for (JSONObject json : list) {
                model.set("PROJECT_ID", json.getString("CONTRACT_ID"));
                model.set("PROJECT_NAME", json.getString("CONTRACT_NAME"));
                model.set("PROJECT_NO", json.getString("CONTRACT_NO"));
                model.set("CONTRACT_ID", json.getString("CONTRACT_ID"));
                model.save();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        renderText("OK");
    }


    public EasyResult actionForAdd() {
        String id = FlowService.getService().getID();
        ProjecContractModel model = getModel(ProjecContractModel.class, "contract");
        model.setCreator(getUserId());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("PROJECT_ID", id);
        model.setPrimaryValues(id);
        try {
            String contractNo = model.getString("CONTRACT_NO");
            if (this.getQuery().queryForExist("select count(1) from YQ_PROJECT_CONTRACT where CONTRACT_NO = ?", contractNo)) {
                this.error("重复数据 contractNo:" + contractNo, null);
                return EasyResult.fail("合同号：" + contractNo + "已经存在，如有疑问请联系管理员");
            }
            if (model.save()) {
                String projectId = model.getString("PROJECT_ID");
                if (StringUtils.notBlank(projectId)) {
                    this.getQuery().executeUpdate("update yq_project set contract_id= ? where project_id = ?", model.getPrimaryValue(), projectId);
                }
                if (this.getQuery().queryForExist("select count(1) from yq_project where project_no = ?", contractNo)) {
                    this.error("重复数据 contractNo:" + contractNo, null);
                    return EasyResult.ok();
                }

                ProjectModel project = new ProjectModel();
                project.set("PROJECT_NO", contractNo);
                project.set("PROJECT_STAGE", "待初验");
                this.synProjectFiled(project,model);
                project.set("PROJECT_ID", model.getPrimaryValue());
                project.set("CONTRACT_ID", model.getPrimaryValue());
                project.set("BEGIN_DATE", StringUtils.defaultIfBlank(model.getString("SIGN_DATE"), model.getString("ADVANCE_EXCUTE_DATE")));
                project.set("SIGN_DATE", StringUtils.defaultIfBlank(model.getString("SIGN_DATE"), model.getString("ADVANCE_EXCUTE_DATE")));
                project.set("CREATE_TIME", EasyDate.getCurrentDateString());
                project.save();

                EasyRecord module = new EasyRecord("yq_project_module", "module_id");
                module.set("module_id", model.getPrimaryValue());
                module.set("project_id", model.getPrimaryValue());
                module.set("module_name", "默认模块");
                module.set("update_time", EasyDate.getCurrentDateString());
                module.set("update_by", getUserId());
                module.set("update_by_name", getUserName());
                this.getQuery().save(module);
                sendWxMsg(getUserName()+"创建了合同",model.getString("CONTRACT_SIMPILE_NAME"));
            } else {
                return EasyResult.fail();
            }
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    
    private void synProjectFiled(ProjectModel project,ProjecContractModel model) {
         project.set("PROJECT_NAME", model.getString("CONTRACT_SIMPILE_NAME"));
         project.set("PRODUCT_LINE", model.getString("PROD_LINE"));
         project.set("PROJECT_DEPT_NAME", model.getString("PROJECT_DEPT_NAME"));
    	 String contractType = model.getString("CONTRACT_TYPE");
         project.set("CONTRACT_TYPE",contractType);
         int signFlag = model.getIntValue("SIGN_FLAG");
         if(signFlag==1) {
         	project.set("PROJECT_TYPE", 11);
         }else if("维保合同".equals(contractType)){
         	project.set("PROJECT_TYPE", 30);
         }else {
         	project.set("PROJECT_TYPE", 10);
         }
    	 project.set("SALES_BY", model.getString("SALES_BY"));
         project.set("SALES_BY_NAME", model.getString("SALES_BY_NAME"));
         project.set("PRE_SALES_BY", model.getString("PM_BY"));
         project.set("PRE_SALES_NAME", model.getString("PM_BY_NAME"));
         project.set("DEV_PLAN_PH", model.getString("DEV_PLAN_PH"));
         project.set("PO_PLAN_PH", model.getString("PO_PLAN_PH"));
         project.set("UPDATE_TIME", EasyDate.getCurrentDateString());
         String contractNo = model.getString("CONTRACT_NO");
         project.set("PROJECT_NO", contractNo);
         
    }
    

    public EasyResult actionForUpdate() {
        String oldAmount = getJsonPara("oldAmount");
        ProjecContractModel model = getModel(ProjecContractModel.class, "contract");
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        String newAmount = model.getString("AMOUNT");
        String contractId = model.getString("CONTRACT_ID");
        try {
            model.update();
            String projectId = model.getString("PROJECT_ID");
            if (StringUtils.notBlank(projectId)) {
            	
            	  ProjectModel project = new ProjectModel();
            	  project.setPrimaryValues(projectId);
            	  project.set("CONTRACT_ID",model.getPrimaryValue());
                  this.synProjectFiled(project, model);
                  project.update();
            }
            if (StringUtils.isNotBlank(oldAmount) && !oldAmount.equals(newAmount)){
                ContractService.getService().changeAmountRatio(contractId);
            }
            ContractService.getService().reloadContractCount(contractId);
            sendWxMsg(getUserName()+"修改合同",model.getString("CONTRACT_SIMPILE_NAME"));
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForDelData() {
        if (hasRole("CONTRACT_MGR")) {
            String id = getJsonPara("id");
            try {
                this.getQuery().executeUpdate("delete from yq_project_contract where contract_id = ?", id);
                this.getQuery().executeUpdate("update yq_project set contract_id = '',is_delete = 1 where contract_id = ?", id);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
            }
        } else {
            return EasyResult.fail("您无权限");
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdateObj() {
        String oldAmount = getJsonPara("oldAmount");
        EasyRecord record = new EasyRecord("YQ_PROJECT_CONTRACT", "CONTRACT_ID");
        String newAmount = record.getString("AMOUNT");
        String contractId = record.getString("CONTRACT_ID");

        try {
            record.setColumns(getJSONObject());
            record.set("UPDATE_BY", getUserId());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(record);
            if (StringUtils.isNotBlank(oldAmount) && !oldAmount.equals(newAmount)){
                ContractService.getService().changeAmountRatio(contractId);
            }
            ContractService.getService().reloadContractCount(contractId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForRefreshCount() {
        try {
            ContractService.getService().reloadAllCount("STAGE_COUNT", "yq_contract_stage");
            ContractService.getService().reloadAllCount("PAYMENT_COUNT", "yq_contract_payment");
            ContractService.getService().reloadAllCount("INCOME_STAGE_COUNT", "yq_contract_income_stage");
            ContractService.getService().reloadAllCount("INCOME_CONFIRM_COUNT", "yq_contract_income_confirm");
            ContractService.getService().reloadAllCount("INCOME_CONFIRM_COUNT_B", "yq_contract_income_confirm");
            ContractService.getService().reloadAllCount("INVOICE_COUNT", "yq_crm_invoice");
            ContractService.getService().reloadAllCount("RECEIPT_COUNT", "yq_contract_receipt");
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();

    }

    public EasyResult actionForRefreshCompleteDateByReceipt() {
        try {
            EasySQL sql = new EasySQL("select STAGE_ID from yq_contract_stage ");
            List<EasyRow> rows = this.getQuery().queryForList(sql.getSQL());
            if (rows == null || rows.size() == 0) {
                return EasyResult.fail("没有找到合同阶段信息");
            }
            for (EasyRow row : rows) {
                String stageId = row.getColumnValue("STAGE_ID");
                ContractService.getService().setCompleteTime(stageId);
            }
         } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }
    
    private void sendWxMsg(String title,String msg) {
    	WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PROJECT,title,msg);
    }

}





