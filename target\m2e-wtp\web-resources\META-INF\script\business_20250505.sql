-- 业务运营平台，所涉及的新表和对旧表的更改sql

-- 客户来源新增三条：电信、联通、友商
INSERT INTO `yq_main`.`easi_pc_dict` (`DICT_TYPE_ID`, `DICT_ID`, `DICT_NAME`, `DICT_DESC`, `P_DICT_ID`, `IDX_ORDER`) VALUES ('Y011', '电信', '电信', '', NULL, 99);
INSERT INTO `yq_main`.`easi_pc_dict` (`DICT_TYPE_ID`, `DICT_ID`, `DICT_NAME`, `DICT_DESC`, `P_DICT_ID`, `IDX_ORDER`) VALUES ('Y011', '联通', '联通', '', NULL, 99);
INSERT INTO `yq_main`.`easi_pc_dict` (`DICT_TYPE_ID`, `DICT_ID`, `DICT_NAME`, `DICT_DESC`, `P_DICT_ID`, `IDX_ORDER`) VALUES ('Y011', '友商', '友商', '', NULL, 99);

-- 为商机表添加新字段
ALTER TABLE `yq_crm_business`
    ADD COLUMN `platform_id` varchar(32) DEFAULT NULL COMMENT '业务平台ID' AFTER `possibility`,
ADD COLUMN `platform_name` varchar(100) DEFAULT NULL COMMENT '业务平台名称' AFTER `platform_id`,
ADD COLUMN `platform_params` varchar(200) DEFAULT NULL COMMENT '平台参数(根据选择的平台填写对应的参数)' AFTER `platform_name`,
ADD COLUMN `expected_win_date` varchar(10) DEFAULT NULL COMMENT '预计赢单日期' AFTER `platform_params`,
ADD COLUMN `actual_win_date` varchar(10) DEFAULT NULL COMMENT '实际赢单日期' AFTER `expected_win_date`,
ADD COLUMN `cost_budget` decimal(10,2) DEFAULT '0.00' COMMENT '预计支出成本' AFTER `amount`;


-- 平台类型表（一级）
CREATE TABLE `yq_business_platform_type` (
                                             `PLATFORM_TYPE_ID` varchar(32) NOT NULL COMMENT '平台类型ID',
                                             `P_PLATFORM_TYPE_ID` varchar(32) DEFAULT NULL COMMENT '父平台类型ID',
                                             `PLATFORM_TYPE_NAME` varchar(100) NOT NULL COMMENT '平台类型名称',
                                             `IDX_ORDER` int(11) DEFAULT NULL COMMENT '排序号',
                                             `STATUS` int(11) DEFAULT '0' COMMENT '状态 0-启用 1-停用',
                                             `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                             `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                             `RESERVE1` varchar(100) DEFAULT NULL COMMENT '预留字段1',
                                             `RESERVE2` varchar(100) DEFAULT NULL COMMENT '预留字段2',
                                             `RESERVE3` varchar(100) DEFAULT NULL COMMENT '预留字段3',
                                             PRIMARY KEY (`PLATFORM_TYPE_ID`) USING BTREE,
                                             KEY `IDX_P_PLATFORM_TYPE_ID` (`P_PLATFORM_TYPE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务平台类型表';

-- 平台表 （二级）
CREATE TABLE `yq_business_platform` (
                                        `PLATFORM_TYPE_ID` varchar(32) NOT NULL COMMENT '平台类型ID',
                                        `PLATFORM_ID` varchar(32) NOT NULL COMMENT '平台ID',
                                        `PLATFORM_CODE` varchar(50) NOT NULL COMMENT '平台编号',
                                        `PLATFORM_NAME` varchar(100) NOT NULL COMMENT '平台名称',
                                        `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `P_PLATFORM_ID` varchar(32) DEFAULT NULL COMMENT '父平台ID',
                                        `STATUS` int(11) DEFAULT '0' COMMENT '状态 0-启用 1-停用',
                                        `IDX_ORDER` int(11) DEFAULT NULL COMMENT '排序号',
                                        `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                        `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                        `RESERVE1` varchar(100) DEFAULT NULL COMMENT '预留字段1',
                                        `RESERVE2` varchar(100) DEFAULT NULL COMMENT '预留字段2',
                                        `RESERVE3` varchar(100) DEFAULT NULL COMMENT '预留字段3',
                                        PRIMARY KEY (`PLATFORM_TYPE_ID`,`PLATFORM_ID`) USING BTREE,
                                        UNIQUE KEY `UK_PLATFORM_CODE` (`PLATFORM_CODE`) USING BTREE,
                                        KEY `IDX_P_PLATFORM_ID` (`P_PLATFORM_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务平台表';

-- 初始化三种一级平台
INSERT INTO `yq_work`.`yq_business_platform_type` (`PLATFORM_TYPE_ID`, `P_PLATFORM_TYPE_ID`, `PLATFORM_TYPE_NAME`, `IDX_ORDER`, `STATUS`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`) VALUES ('642e7f978a4348759160fcdf4d08b376', '0', '合作运营平台', 99, 0, '2025-04-29 14:05:32', '2025-04-29 14:05:32', NULL, NULL, NULL);
INSERT INTO `yq_work`.`yq_business_platform_type` (`PLATFORM_TYPE_ID`, `P_PLATFORM_TYPE_ID`, `PLATFORM_TYPE_NAME`, `IDX_ORDER`, `STATUS`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`) VALUES ('7328528f0a18462a8afac9569221c6a8', '0', 'SAAS平台', 99, 0, '2025-04-29 10:38:32', '2025-04-29 14:05:57', NULL, NULL, NULL);
INSERT INTO `yq_work`.`yq_business_platform_type` (`PLATFORM_TYPE_ID`, `P_PLATFORM_TYPE_ID`, `PLATFORM_TYPE_NAME`, `IDX_ORDER`, `STATUS`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`) VALUES ('bee6fd2e54ba4a3eb6d5eb9db2f731d6', '0', 'PAAS平台', 99, 0, '2025-04-29 11:08:48', '2025-04-29 14:06:10', NULL, NULL, NULL);

-- 初始化目前业务平台
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('642e7f978a4348759160fcdf4d08b376', '18be464893754768943ec9d5c2486231', 'GuangDongTianYiYunZongJi', '广东天翼云总机', NULL, 0, 99, '2025-05-06 19:32:10', '2025-05-06 19:32:10', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('642e7f978a4348759160fcdf4d08b376', '7463cce409164fc28a76bf8356a46c44', 'HuNanTianYiYunZongJi', '湖南天翼云总机', NULL, 0, 99, '2025-05-06 19:32:02', '2025-05-06 19:32:02', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('642e7f978a4348759160fcdf4d08b376', 'c41576fed68d43a7b38392bb63c28dae', 'GuangDongDianXinYunYingYong', '广东电信云应用', NULL, 0, 99, '2025-05-06 19:32:19', '2025-05-06 19:32:19', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('7328528f0a18462a8afac9569221c6a8', '130154abfff7441983e9ae16c516a4a8', 'TianYiYunXinTongHua', '天翼云新通话', NULL, 0, 99, '2025-05-06 19:31:00', '2025-05-06 19:31:00', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('7328528f0a18462a8afac9569221c6a8', 'e805291b6c1e49668462495c3754b785', 'ALiYunYunHu', '阿里云云呼', NULL, 0, 99, '2025-05-06 19:31:17', '2025-05-06 19:31:17', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('bee6fd2e54ba4a3eb6d5eb9db2f731d6', 'ea74639ea75243acb269905732dfa014', 'DaiLi', '代理', NULL, 0, 99, '2025-05-06 19:31:53', '2025-05-06 19:31:53', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('bee6fd2e54ba4a3eb6d5eb9db2f731d6', 'f203bcf598a64deb90a3e876380e563b', 'YuYin', '语音', NULL, 0, 99, '2025-05-06 19:31:34', '2025-05-06 19:31:34', NULL, NULL, NULL, '');
INSERT INTO `yq_work`.`yq_business_platform` (`PLATFORM_TYPE_ID`, `PLATFORM_ID`, `PLATFORM_CODE`, `PLATFORM_NAME`, `P_PLATFORM_ID`, `STATUS`, `IDX_ORDER`, `CREATE_TIME`, `UPDATE_TIME`, `RESERVE1`, `RESERVE2`, `RESERVE3`, `REMARK`) VALUES ('bee6fd2e54ba4a3eb6d5eb9db2f731d6', 'fe69b3bc5ca342bcb5b6009eb4a26136', 'DuanXin', '短信', NULL, 0, 99, '2025-05-06 19:31:45', '2025-05-06 19:31:45', NULL, NULL, NULL, '');

-- 供应商表加字段
ALTER TABLE yq_erp_supplier
 ADD COLUMN    `operator_id` varchar(32) DEFAULT NULL COMMENT '运营负责人ID',
 ADD COLUMN   `operator_name` varchar(12) DEFAULT NULL COMMENT '运营负责人姓名',
 ADD COLUMN   `tech_id` varchar(32) DEFAULT NULL COMMENT '技术负责人ID',
 ADD COLUMN   `tech_name` varchar(12) DEFAULT NULL COMMENT '技术负责人姓名',
 ADD COLUMN    `status` tinyint(4) DEFAULT 0 COMMENT '供应商状态:0-正常 1-暂停 2-下架';


-- 新增：客户运营-业务销售价格表
CREATE TABLE `yq_crm_sales_price` (
                                      `PRICE_ID` varchar(32) NOT NULL COMMENT '价格ID',
                                      `CUST_ID` varchar(32) NOT NULL COMMENT '客户id',
                                      `CUSTOMER_NAME` varchar(100) DEFAULT NULL,
                                      `PRODUCT_ID` varchar(32) DEFAULT NULL COMMENT '产品ID',
                                      `PRODUCT_NAME` varchar(255) DEFAULT NULL COMMENT '产品名称',
                                      `PLATFORM_ID` varchar(32) DEFAULT NULL COMMENT '业务平台ID',
                                      `PLATFORM_NAME` varchar(255) DEFAULT NULL,
                                      `PRICE_TYPE` varchar(20) DEFAULT NULL COMMENT '价格类型',
                                      `PRICE_UNIT` varchar(20) DEFAULT NULL COMMENT '价格单位(月/分钟/条)',
                                      `SALES_PRICE` decimal(10,2) DEFAULT '0.00' COMMENT '销售价格',
                                      `SALE_TAX_RATE` tinyint(4) DEFAULT NULL COMMENT '销售税率',
                                      `SUPPLIER_ID` varchar(32) DEFAULT NULL COMMENT '供应商ID',
                                      `SUPPLIER_NAME` varchar(100) DEFAULT NULL COMMENT '供应商名称',
                                      `SUPPLIER_PRICE` decimal(10,2) DEFAULT '0.00' COMMENT '供应商价格',
                                      `BUY_TAX_RATE` int(4) DEFAULT NULL COMMENT '采购税率',
                                      `PAYMENT_METHOD` varchar(50) DEFAULT NULL COMMENT '付款方式',
                                      `REMARK` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `STATUS` tinyint(4) DEFAULT '0' COMMENT '状态:0-正常 1-停用',
                                      `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                      `UPDATE_TIME` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                      `CREATOR` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                      `CREATE_NAME` varchar(30) DEFAULT NULL COMMENT '创建人姓名',
                                      PRIMARY KEY (`PRICE_ID`) USING BTREE,
                                      KEY `idx_cust_id` (`CUST_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售价格表';


-- 新增：客户运营-成本表
CREATE TABLE `yq_crm_cost` (
                               `COST_ID` varchar(32) NOT NULL COMMENT '成本ID',
                               `DATE_ID` varchar(10) DEFAULT NULL COMMENT '成本归属日期',
                               `MONTH_ID` varchar(7) DEFAULT NULL COMMENT '成本归属月份',
                               `CONTRACT_ID` varchar(32) DEFAULT NULL COMMENT '合同ID',
                               `CONTRACT_NAME` varchar(255) DEFAULT NULL COMMENT '合同名称',
                               `CUST_ID` varchar(32) DEFAULT NULL COMMENT '客户ID',
                               `CUST_NAME` varchar(255) DEFAULT NULL COMMENT '客户名称',
                               `OWNER_ID` varchar(32) DEFAULT NULL COMMENT '负责人ID',
                               `OWNER_NAME` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
                               `COST_TYPE` varchar(20) DEFAULT NULL COMMENT '成本类型(硬件/服务/其他)',
                               `COST_NAME` varchar(255) DEFAULT NULL COMMENT '成本名称',
                               `NUMBER` int(11) DEFAULT '0' COMMENT '采购数量',
                               `UNIT_PRICE` decimal(10,2) DEFAULT '0.00' COMMENT '单价',
                               `TOTAL_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '总成本(含税)',
                               `TAX_RATE` decimal(10,2) DEFAULT '0.00' COMMENT '税率',
                               `NO_TAX_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '总成本(不含税)',
                               `PAY_TIME` varchar(19) DEFAULT NULL COMMENT '付款时间',
                               `ORDER_ID` varchar(32) DEFAULT NULL COMMENT '采购订单号',
                               `ORDER_TIME` varchar(19) DEFAULT NULL COMMENT '采购下单时间',
                               `SUPPLIER_ID` varchar(32) DEFAULT NULL COMMENT '供应商ID',
                               `SUPPLIER_NAME` varchar(100) DEFAULT NULL COMMENT '供应商名称',
                               `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
                               `RELATE_TYPE` tinyint(4) DEFAULT '0' COMMENT '关联类型(0无关联、1关联采购、2关联报销)',
                               `BX_APPLY_ID` varchar(32) DEFAULT NULL COMMENT '报销申请号',
                               `BX_ITEM_ID` varchar(32) DEFAULT NULL COMMENT '报销明细号',
                               `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间',
                               `UPDATE_TIME` varchar(19) DEFAULT NULL COMMENT '更新时间',
                               `CREATOR` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                               `CREATE_NAME` varchar(30) DEFAULT NULL COMMENT '创建人姓名',
                               PRIMARY KEY (`COST_ID`) USING BTREE,
                               KEY `IDX_CUST_ID` (`CUST_ID`) USING BTREE,
                               KEY `IDX_CONTRACT_ID` (`CONTRACT_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成本表';