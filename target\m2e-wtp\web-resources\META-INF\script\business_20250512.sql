-- 20250512 合同模块中的开票表，加上平台关联信息，开票关联流程
ALTER TABLE yq_crm_invoice
    ADD COLUMN `PLATFORM_ID` varchar(32) DEFAULT NULL COMMENT '业务平台ID',
    ADD COLUMN `PLATFORM_NAME` varchar(100) DEFAULT NULL COMMENT '业务平台名称' AFTER `PLATFORM_ID`,
    ADD COLUMN `MONTH_ID` varchar(10) DEFAULT NULL COMMENT '开票日期的月份',
    ADD COLUMN `KP_APPLY_ID` varchar(32) DEFAULT NULL COMMENT '开票申请号',
    ADD COLUMN `APPLY_TITLE` varchar(255)  DEFAULT NULL COMMENT '开票流程标题';

-- 补之前的month_id
UPDATE yq_crm_invoice
SET MONTH_ID = DATE_FORMAT(STR_TO_DATE(mark_date,'%Y-%m-%d'),'%Y%m')
WHERE MONTH_ID IS null;