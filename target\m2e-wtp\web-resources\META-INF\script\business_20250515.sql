-- 250515 新建业务平台订购表
CREATE TABLE `yq_crm_platform_order` (
                                         `ORDER_ID` varchar(32) NOT NULL COMMENT '订购ID',
                                         `PLATFORM_ID` varchar(32) NOT NULL COMMENT '平台ID',
                                         `PLATFORM_NAME` varchar(100) DEFAULT NULL COMMENT '平台名称',
                                         `CONTRACT_ID` varchar(32) NOT NULL COMMENT '合同ID',
                                         `CONTRACT_NAME` varchar(255) DEFAULT NULL COMMENT '合同名称',
                                         `CONTRACT_NO` varchar(50) DEFAULT NULL COMMENT '合同编号',
                                         `BUSINESS_ID` varchar(32) DEFAULT NULL COMMENT '商机ID',
                                         `BUSINESS_NAME` varchar(50) DEFAULT NULL COMMENT '商机名称',
                                         `CUST_ID` varchar(32) NOT NULL COMMENT '客户ID',
                                         `CUST_NAME` varchar(100) DEFAULT NULL COMMENT '客户名称',
                                         `SALES_BY` varchar(32) DEFAULT NULL COMMENT '销售ID',
                                         `SALES_BY_NAME` varchar(30) DEFAULT NULL COMMENT '销售姓名',
                                         `ORDER_STATUS` tinyint(4) DEFAULT '0' COMMENT '订购状态:0-正常 1-暂停 2-终止',
                                         `START_DATE` varchar(10) DEFAULT NULL COMMENT '开始日期',
                                         `END_DATE` varchar(10) DEFAULT NULL COMMENT '结束日期',
                                         `ORDER_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '订购金额',
                                         `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
                                         `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                         `UPDATE_TIME` varchar(19) DEFAULT NULL COMMENT '更新时间',
                                         `CREATOR` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                         PRIMARY KEY (`ORDER_ID`) USING BTREE,
                                         KEY `IDX_CONTRACT_ID` (`CONTRACT_ID`) USING BTREE,
                                         KEY `IDX_CUST_ID` (`CUST_ID`) USING BTREE,
                                         KEY `IDX_PLATFORM_ID` (`PLATFORM_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台订购表';


-- 250519 增加业务订购的字段
ALTER TABLE yq_crm_sales_price
    ADD COLUMN `PLATFORM_ORDER_ID` varchar(32) DEFAULT NULL COMMENT '客户订购业务信息ID' AFTER `CUSTOMER_NAME`;

-- 250522 成本表 增加业务订购的字段
ALTER TABLE yq_crm_cost
    ADD COLUMN `PLATFORM_ORDER_ID` varchar(32) DEFAULT NULL COMMENT '客户订购业务信息ID' AFTER `COST_OWNER`;

-- 250522 删结算表多余字段
ALTER TABLE yq_crm_settlement
    DROP COLUMN BU_DEPT,
    DROP COLUMN SALES_ID,
    DROP COLUMN SALES_NAME,
    DROP COLUMN PROJECT_NAME;


-- 250523 成本表加字段（解决结算和成本、发票的一致性问题）
ALTER TABLE yq_crm_cost
   ADD COLUMN `SETTLEMENT_ID` varchar(32) DEFAULT NULL COMMENT '结算ID',
   ADD COLUMN `SETTLEMENT_NO` varchar(100) DEFAULT NULL COMMENT '结算编号';

-- 250523 发票表添加字段
ALTER TABLE yq_crm_invoice
   ADD COLUMN `SETTLEMENT_ID` varchar(32) DEFAULT NULL COMMENT '结算ID',
   ADD COLUMN `SETTLEMENT_NO` varchar(100) DEFAULT NULL COMMENT '结算编号';


-- 250523 结算表-毛利率调整精度
ALTER TABLE yq_crm_settlement
    MODIFY COLUMN `GROSS_PROFIT_RATE` decimal(10,2) DEFAULT '0.00' COMMENT '毛利率(%)';

-- 250603 结算表-关联合同编号
update yq_crm_settlement t1 inner join yq_project_contract t2 on t1.CONTRACT_ID = t2.CONTRACT_ID
    SET t1.CONTRACT_NO = t2.CONTRACT_NO;

--250604 结算表添加开票日期字段
ALTER TABLE yq_crm_settlement
    ADD COLUMN `MARK_DATE` varchar(10) DEFAULT NULL COMMENT '开票日期';