DROP TABLE IF EXISTS `iom_order`;
CREATE TABLE `iom_order` (
  `ORDER_ID` varchar(32) NOT NULL,
  `CUST_ENT_NAME` varchar(50) DEFAULT NULL COMMENT '客户名称',
  `CUST_WORK_ADDR` varchar(30) DEFAULT NULL COMMENT '客户工作地址',
  `SYSTEM_TYPE` int(11) DEFAULT NULL COMMENT '坐席使用系统',
  `NETWORK_TYPE` int(11) DEFAULT NULL COMMENT '接入方式',
  `SPECIAL_LINE_NO` varchar(50) DEFAULT NULL COMMENT '专线编号',
  `CONTRACT_NO` varchar(50) DEFAULT NULL COMMENT '合同编号',
  `NUMBERS` varchar(500) DEFAULT NULL COMMENT '外显号码',
  `CUST_PHONE` varchar(30) DEFAULT NULL,
  `CUST_NAME` varchar(30) DEFAULT NULL,
  `CUST_IT_PHONE` varchar(30) DEFAULT NULL,
  `CUST_IT_NAME` varchar(30) DEFAULT NULL COMMENT '客户技术',
  `CUST_MGR_PHONE` varchar(30) DEFAULT NULL,
  `CUST_MGR_NAME` varchar(30) DEFAULT NULL COMMENT '客户经理',
  `AGENT_NUM` int(11) DEFAULT NULL,
  `AGENT_PRICE` decimal(10,0) DEFAULT NULL,
  `ORDER_STATE` int(11) DEFAULT NULL COMMENT '工单状态 1:已提交  2、已阅读 3、审核通过 4、工单退回 5、待实施  6、实施完毕 ',
  `ORDER_TYPE` int(11) DEFAULT NULL COMMENT '工单类型',
  `ORDER_REMARK` mediumtext,
  `CREATE_TIME` varchar(19) DEFAULT NULL,
  `ORDER_CHECK_BY` varchar(32) DEFAULT NULL COMMENT '审核人',
  `ORDER_CHECK_TIME` varchar(19) DEFAULT NULL,
  `ORDER_HANDLE_BY` varchar(32) DEFAULT NULL COMMENT '实施人',
  `ORDER_WILL_TIME` varchar(19) DEFAULT NULL COMMENT '要求工单完成时间',
  `ORDER_FINISH_TIME` varchar(19) DEFAULT NULL COMMENT '工单实际竣工时间',
  `CREATER` varchar(32) DEFAULT NULL,
  `CREATER_NAME` varchar(20) DEFAULT NULL,
  `CREATER_DEPT_ID` varchar(32) DEFAULT NULL COMMENT '所属部门',
  PRIMARY KEY (`ORDER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `iom_order_his`;
CREATE TABLE `iom_order_his` (
  `order_his_id` varchar(32) NOT NULL,
  `order_id` varchar(32) DEFAULT NULL,
  `user_name` varchar(30) DEFAULT NULL,
  `user_id` varchar(32) DEFAULT NULL,
  `action` varchar(30) DEFAULT NULL,
  `remark` varchar(500) DEFAULT NULL,
  `handle_time` varchar(19) DEFAULT NULL,
  `total_time` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`order_his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `iom_order_files`;
CREATE TABLE `iom_order_files` (
  `ORDER_FILE_ID` varchar(32) NOT NULL,
  `ORDER_ID` varchar(32) DEFAULT NULL,
  `FILE_ID` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`ORDER_FILE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



