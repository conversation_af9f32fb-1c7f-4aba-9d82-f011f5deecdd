<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>事务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5 id="title">发件箱</h5>
	          		     <div class="input-group input-group-sm" style="width: 180px">
	          		    	<span class="input-group-addon">标题</span>	
                     		<input data-mars-reload="false" type="text" name="title" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="taskMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
  <script type="text/javascript">
		var taskMgr={
			init:function(){
				$("#taskMgrForm").initTable({
					mars:'AffairDao.sendAffairList',
					height:'full-95',
					id:'taskMgrTable',
					limit:20,
					even: true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'AFFAIR_NAME',
						title: '主题',
						align:'left',
						minWidth:220,
						event:'taskMgr.detail',
						style:'color:#036fe2;cursor:pointer',
						templet:function(row){
							var affairState = row["AFFAIR_STATE"];
							var affairName = row['AFFAIR_NAME'];
							if(affairState=='0'){
								return '<span class="layui-badge ml-5">草稿</span> '+affairName;
							}
							if(affairState=='9'){
								return '<span class="layui-badge layui-bg-orange ml-5">已删除</span> '+affairName;
							}
							if(affairState=='10'){
								return '<span class="layui-badge layui-bg-orange ml-5">待发送</span> '+affairName;
							}
							if(affairState=='20'){
								return '<span class="layui-badge layui-bg-green ml-5">已发送</span> '+affairName;
							}
							if(affairState=='21'){
								return '<span class="layui-badge layui-bg-cyan ml-5">暂停</span> '+affairName;
							}
							if(row['LOOK_COUNT']==0){
								return '<span class="layui-badge ml-5">新</span>'+affairName;
							}
							return affairName;
						}
					},{
					    field: 'CREATE_NAME',
						title: '发件人',
						align:'left',
						width:120
					},{
					    field: 'RECEIVER_USER_NAME',
						title: '收件人',
						align:'left',
						width:120
					},{
					    field: 'CREATE_TIME',
						title: '已读/收件人',
						align:'center',
						width:100,
						templet:'<div>{{d.LOOK_COUNT}}/{{d.RECEIVER_COUNT}}</div>'
						
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:140
					},{
						field:'LAST_REPLY_TIME',
						title:'最新回复时间',
						width:140,
						hide:true
					},{
					    field: 'SEND_TYPE',
						title: '发送时间',
						align:'left',
						width:120,
						templet:function(row){
							var sendType = row['SEND_TYPE'];
							if(sendType=='0'){
								return '立即发送';
							}
							if(sendType=='10'){
								return row['EXCUTE_TIME'];
							}
							if(sendType=='20'){
								return "每月"+row['MONTH_DAYS']+"号";
							}
							return '';
						}
					}
				]],done:function(){
					
			   }});
			},
			query:function(){
				$("#taskMgrForm").render();
				$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
			},
			detail:function(data){
				let url = '${ctxPath}/affair/'+data.AFFAIR_ID+"?source=send";
				$.get(url,{},function(result){
					window.location.hash = "#"+data.AFFAIR_ID;
  					$(".right-content").html(result);
				});
			}
		}
		$(function(){
			$("#taskMgrForm").render({success:function(result){
				taskMgr.init();
			}});
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>